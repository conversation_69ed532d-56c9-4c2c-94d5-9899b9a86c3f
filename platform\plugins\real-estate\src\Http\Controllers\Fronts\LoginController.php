<?php

namespace Xmetr\RealEstate\Http\Controllers\Fronts;

use Xmetr\ACL\Traits\AuthenticatesUsers;
use Xmetr\ACL\Traits\LogoutGuardTrait;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Forms\Fronts\Auth\LoginForm;
use Xmetr\RealEstate\Http\Controllers\BaseController;
use Xmetr\RealEstate\Http\Requests\Fronts\Auth\LoginRequest;
use Xmetr\SeoHelper\Facades\SeoHelper;
use Xmetr\Theme\Facades\Theme;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class LoginController extends BaseController
{
    use AuthenticatesUsers, LogoutGuardTrait {
        AuthenticatesUsers::attemptLogin as baseAttemptLogin;
    }

    public string $redirectTo = '/';

    public function __construct()
    {
        parent::__construct();

        $this->redirectTo = route('public.account.settings');
    }

    public function showLoginForm()
    {
        abort_unless(RealEstateHelper::isLoginEnabled(), 404);

        SeoHelper::setTitle(trans('plugins/real-estate::account.login'));

        return Theme::scope(
            'real-estate.account.auth.login',
            ['form' => LoginForm::create()],
            'plugins/real-estate::themes.auth.login'
        )->render();
    }

    protected function guard()
    {
        return auth('account');
    }

    public function login(LoginRequest $request)
    {
        abort_unless(RealEstateHelper::isLoginEnabled(), 404);

        // Store intended URL if provided in form
        if ($request->has('intended_url') && $request->input('intended_url')) {
            session(['url.intended' => $request->input('intended_url')]);
        }

        $request->merge([$this->username() => $request->input('email')]);

        $this->validateLogin($request);

        // If the class is using the ThrottlesLogins trait, we can automatically throttle
        // the login attempts for this application. We'll key this by the username and
        // the IP address of the client making these requests into this application.
        if ($this->hasTooManyLoginAttempts($request)) {
            $this->fireLockoutEvent($request);

            $this->sendLockoutResponse($request);
        }

        if ($this->attemptLogin($request)) {
            return $this->sendLoginResponse($request);
        }

        // If the login attempt was unsuccessful we will increment the number of attempts
        // to login and redirect the user back to the login form. Of course, when this
        // user surpasses their maximum number of attempts they will get locked out.
        $this->incrementLoginAttempts($request);

        return $this->sendFailedLoginResponse();
    }

    protected function attemptLogin(Request $request)
    {
        if ($this->guard()->validate($this->credentials($request))) {
            $account = $this->guard()->getLastAttempted();

            if (setting(
                'verify_account_email',
                false
            ) && empty($account->confirmed_at)) {
                throw ValidationException::withMessages([
                    'confirmation' => [
                        __('The given email address has not been confirmed. <a href=":resend_link">Resend confirmation link.</a>', [
                            'resend_link' => route('public.account.resend_confirmation', ['email' => $account->email]),
                        ]),
                    ],
                ]);
            }

            return $this->baseAttemptLogin($request);
        }

        return false;
    }

    public function username()
    {
        return filter_var(request()->input('email'), FILTER_VALIDATE_EMAIL) ? 'email' : 'username';
    }

    protected function sendLoginResponse(Request $request)
    {
        $request->session()->regenerate();

        $this->clearLoginAttempts($request);

        // Update last login timestamp for the account
        $this->guard()->user()->update(['last_login' => Carbon::now()]);

        $this->authenticated($request, $this->guard()->user());

        if ($request->wantsJson() || $request->ajax()) {
            // For AJAX requests (modal forms), return the intended URL
            $intendedUrl = session('url.intended', $this->redirectPath());
            session()->forget('url.intended'); // Clear the intended URL after use

            return response()->json([
                'error' => false,
                'message' => __('Login successful'),
                'data' => [
                    'redirect_url' => $intendedUrl
                ]
            ]);
        }

        return redirect()->intended($this->redirectPath());
    }

    public function logout(Request $request)
    {
        abort_unless(RealEstateHelper::isLoginEnabled(), 404);

        // Determine redirect URL before logout (from referer or current page)
        $redirectUrl = $this->determineLogoutRedirectUrl($request);

        // Perform logout on the account guard
        $this->guard()->logout();

        // Clear any social login session data
        $this->clearSocialLoginSessionData($request);

        // Count active guards to determine if we should flush the entire session
        $activeGuards = 0;
        foreach (config('auth.guards', []) as $guard => $guardConfig) {
            if ($guardConfig['driver'] !== 'session') {
                continue;
            }
            if ($this->isActiveGuard($request, $guard)) {
                $activeGuards++;
            }
        }

        // If no other guards are active, flush and regenerate the session
        if (! $activeGuards) {
            $request->session()->flush();
            $request->session()->regenerate();
        } else {
            // If other guards are active, just regenerate the token for security
            $request->session()->regenerateToken();
        }

        $this->loggedOut($request);

        // Handle AJAX requests differently
        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'error' => false,
                'message' => __('Logout successful'),
                'data' => [
                    'redirect_url' => $redirectUrl
                ]
            ]);
        }

        return redirect($redirectUrl);
    }

    /**
     * Store intended URL for redirect after login
     * Used when modal is opened manually
     */
    public function storeIntendedUrl(Request $request)
    {
        if ($request->ajax() && $request->has('intended_url')) {
            $intendedUrl = $request->input('intended_url');

            // Only store if it's a valid URL and not already a login/register page
            if (filter_var($intendedUrl, FILTER_VALIDATE_URL) &&
                !str_contains($intendedUrl, '/login') &&
                !str_contains($intendedUrl, '/register')) {
                session(['url.intended' => $intendedUrl]);
            }
        }

        return response()->json(['success' => true]);
    }

    /**
     * Clear social login session data during logout
     */
    protected function clearSocialLoginSessionData(Request $request): void
    {
        $request->session()->forget([
            'social_login_guard_current',
            'social_login_provider_current',
            'social_login_request_time',
            'social_login_request_url',
            'url.intended',
        ]);
    }

    /**
     * Determine where to redirect after logout
     */
    protected function determineLogoutRedirectUrl(Request $request): string
    {
        // Priority 1: Check for redirect_url parameter
        if ($request->has('redirect_url') && $request->input('redirect_url')) {
            $redirectUrl = $request->input('redirect_url');
            if ($this->isValidLogoutRedirectUrl($redirectUrl)) {
                return $redirectUrl;
            }
        }

        // Priority 2: Use HTTP referer (the page user was on when they clicked logout)
        $referer = $request->header('referer');
        if ($referer && $this->isValidLogoutRedirectUrl($referer)) {
            return $referer;
        }

        // Priority 3: Use previous URL from session
        $previousUrl = session()->previousUrl();
        if ($previousUrl && $this->isValidLogoutRedirectUrl($previousUrl)) {
            return $previousUrl;
        }

        // Fallback: Homepage
        return route('public.index');
    }

    /**
     * Check if a URL is valid for logout redirect
     */
    protected function isValidLogoutRedirectUrl(string $url): bool
    {
        // Must be a valid URL
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        // Must be from the same domain
        $urlHost = parse_url($url, PHP_URL_HOST);
        $appHost = parse_url(config('app.url'), PHP_URL_HOST);
        if ($urlHost !== $appHost) {
            return false;
        }

        // Exclude auth-related and admin pages
        $excludedPaths = ['/login', '/register', '/auth/', '/password/', '/admin', '/account/settings', '/account/'];
        foreach ($excludedPaths as $path) {
            if (str_contains($url, $path)) {
                return false;
            }
        }

        return true;
    }
}
