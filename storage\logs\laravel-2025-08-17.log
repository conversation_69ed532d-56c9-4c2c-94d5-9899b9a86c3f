[2025-08-17 17:57:45] local.ERROR: syntax error, unexpected token ";" {"userId":1,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \";\" at D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php:301)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#1 D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\RealEstateServiceProvider.php(114): Composer\\Autoload\\ClassLoader->loadClass('Xmetr\\\\RealEstat...')
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Xmetr\\RealEstate\\Providers\\RealEstateServiceProvider->Xmetr\\RealEstate\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('Xmetr\\\\RealEstat...', Array, true)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('Xmetr\\\\RealEstat...', Array)
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('Xmetr\\\\RealEstat...', Array)
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(120): Illuminate\\Foundation\\Application->make('Xmetr\\\\RealEstat...', Array)
#8 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\src\\Http\\Controllers\\XmetrController.php(87): app('Xmetr\\\\RealEstat...')
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Theme\\Xmetr\\Http\\Controllers\\XmetrController->ajaxGetPropertiesForMap(Object(Illuminate\\Http\\Request))
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('ajaxGetProperti...', Array)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Theme\\Xmetr\\Http\\Controllers\\XmetrController), 'ajaxGetProperti...')
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\RequiresJsonRequestMiddleware.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\RequiresJsonRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->Xmetr\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\xmetr\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIncorrectLanguagePrefix->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#46 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#55 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#78 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#80 {main}
"} 
[2025-08-17 18:39:01] local.ERROR: Undefined variable $rented_overlay {"view":{"view":"D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\properties\\item-grid.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-774398059 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#3662</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-774398059\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","property":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Xmetr\\RealEstate\\Models\\Property</span> {<a class=sf-dump-ref>#4577</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">re_properties</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:53</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>762</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Long-term rent 85 m&#178; duplex loft with pool in Colegiales, Buenos Aires</span>\"
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">rent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"\"
    \"<span class=sf-dump-key>original_description</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"1034 characters\">&lt;p&gt;Duplex loft for rent for a year in Colegiales, available! &#127968;&lt;/p&gt;&lt;p&gt;Area: Colegiales. Block address: Dorrego 1900. Mafalda, Clemente and Bartolome Mitre squares are 3 blocks away. Metro line D and the commuter train station are 6 blocks away.&lt;/p&gt;&lt;p&gt;The historic building Silos de Dorrego is a combination of industrial charm and modern comfort. In the 1990s, the building was transformed into the first loft complex in South America, preserving the factory structure and adapting it for luxury housing.&lt;/p&gt;&lt;p&gt;Description: 2 rooms 85m&#178;. Below is a spacious living-dining room, separate kitchen. Above is a bedroom with a double bed. Air conditioning. 2 bathrooms (bath). Balcony.&lt;br&gt;The complex has a heated swimming pool, a spacious gym, a paddle court, a sauna, a garden and a playground for children, 2 elevators in the building.&lt;/p&gt;&lt;p&gt;Terms: 1000 USD + deposit 1 month + expenses (expensas) ~427,000 pesos + utilities. Annual contract! Advance payment 1 month.&lt;/p&gt;&lt;p&gt;Commission: 850 USD. Check the commission for another term.&lt;/p&gt;</span>\"
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"79 characters\">Av. Dorrego 1900, C1414 Cdad. Aut&#243;noma de Buenos Aires, &#1041;&#1091;&#1077;&#1085;&#1086;&#1089;-&#1040;&#1081;&#1088;&#1077;&#1089;, &#1040;&#1088;&#1075;&#1077;&#1085;&#1090;&#1080;&#1085;&#1072;</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"384 characters\">{&quot;1&quot;:&quot;.\\/photo-2025-04-21-205256.webp&quot;,&quot;2&quot;:&quot;.\\/photo-2025-04-21-205305.webp&quot;,&quot;3&quot;:&quot;.\\/photo-2025-04-21-205304.webp&quot;,&quot;4&quot;:&quot;.\\/photo-2025-04-21-205303.webp&quot;,&quot;5&quot;:&quot;.\\/photo-2025-04-21-205302-1.webp&quot;,&quot;6&quot;:&quot;.\\/photo-2025-04-21-205302.webp&quot;,&quot;7&quot;:&quot;.\\/photo-2025-04-21-205300.webp&quot;,&quot;8&quot;:&quot;.\\/photo-2025-04-21-205301.webp&quot;,&quot;9&quot;:&quot;.\\/photo-2025-04-21-205259.webp&quot;,&quot;10&quot;:&quot;.\\/photo-2025-04-21-205258.webp&quot;}</span>\"
    \"<span class=sf-dump-key>video</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>video_thumbnail</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>floor_plans</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>number_bedroom</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>number_bathroom</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>number_floor</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>square</span>\" => <span class=sf-dump-num>85.0</span>
    \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1000.00</span>\"
    \"<span class=sf-dump-key>commission</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>deposit</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-num>8175</span>
    \"<span class=sf-dump-key>district_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>period</span>\" => \"<span class=sf-dump-str title=\"5 characters\">month</span>\"
    \"<span class=sf-dump-key>rental_period</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>required_documents</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>suitable_for</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">renting</span>\"
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-num>114</span>
    \"<span class=sf-dump-key>author_type</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Xmetr\\RealEstate\\Models\\Account</span>\"
    \"<span class=sf-dump-key>moderation_status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">approved</span>\"
    \"<span class=sf-dump-key>reject_reason</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>expire_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-05-21</span>\"
    \"<span class=sf-dump-key>auto_renew</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>bills_included</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>utilities</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>furnished</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>pets_allowed</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>smoking_allowed</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>online_view_tour</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>never_expired</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-21 20:57:22</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-21 20:59:45</span>\"
    \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-34.5800974</span>\"
    \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-58.4417991</span>\"
    \"<span class=sf-dump-key>views</span>\" => <span class=sf-dump-num>153</span>
    \"<span class=sf-dump-key>whatsapp_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>telegram_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>phone_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>22</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>unique_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>private_notes</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:53</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>762</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Long-term rent 85 m&#178; duplex loft with pool in Colegiales, Buenos Aires</span>\"
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">rent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"\"
    \"<span class=sf-dump-key>original_description</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"1034 characters\">&lt;p&gt;Duplex loft for rent for a year in Colegiales, available! &#127968;&lt;/p&gt;&lt;p&gt;Area: Colegiales. Block address: Dorrego 1900. Mafalda, Clemente and Bartolome Mitre squares are 3 blocks away. Metro line D and the commuter train station are 6 blocks away.&lt;/p&gt;&lt;p&gt;The historic building Silos de Dorrego is a combination of industrial charm and modern comfort. In the 1990s, the building was transformed into the first loft complex in South America, preserving the factory structure and adapting it for luxury housing.&lt;/p&gt;&lt;p&gt;Description: 2 rooms 85m&#178;. Below is a spacious living-dining room, separate kitchen. Above is a bedroom with a double bed. Air conditioning. 2 bathrooms (bath). Balcony.&lt;br&gt;The complex has a heated swimming pool, a spacious gym, a paddle court, a sauna, a garden and a playground for children, 2 elevators in the building.&lt;/p&gt;&lt;p&gt;Terms: 1000 USD + deposit 1 month + expenses (expensas) ~427,000 pesos + utilities. Annual contract! Advance payment 1 month.&lt;/p&gt;&lt;p&gt;Commission: 850 USD. Check the commission for another term.&lt;/p&gt;</span>\"
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"79 characters\">Av. Dorrego 1900, C1414 Cdad. Aut&#243;noma de Buenos Aires, &#1041;&#1091;&#1077;&#1085;&#1086;&#1089;-&#1040;&#1081;&#1088;&#1077;&#1089;, &#1040;&#1088;&#1075;&#1077;&#1085;&#1090;&#1080;&#1085;&#1072;</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"384 characters\">{&quot;1&quot;:&quot;.\\/photo-2025-04-21-205256.webp&quot;,&quot;2&quot;:&quot;.\\/photo-2025-04-21-205305.webp&quot;,&quot;3&quot;:&quot;.\\/photo-2025-04-21-205304.webp&quot;,&quot;4&quot;:&quot;.\\/photo-2025-04-21-205303.webp&quot;,&quot;5&quot;:&quot;.\\/photo-2025-04-21-205302-1.webp&quot;,&quot;6&quot;:&quot;.\\/photo-2025-04-21-205302.webp&quot;,&quot;7&quot;:&quot;.\\/photo-2025-04-21-205300.webp&quot;,&quot;8&quot;:&quot;.\\/photo-2025-04-21-205301.webp&quot;,&quot;9&quot;:&quot;.\\/photo-2025-04-21-205259.webp&quot;,&quot;10&quot;:&quot;.\\/photo-2025-04-21-205258.webp&quot;}</span>\"
    \"<span class=sf-dump-key>video</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>video_thumbnail</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>floor_plans</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>number_bedroom</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>number_bathroom</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>number_floor</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>square</span>\" => <span class=sf-dump-num>85.0</span>
    \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1000.00</span>\"
    \"<span class=sf-dump-key>commission</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>deposit</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-num>8175</span>
    \"<span class=sf-dump-key>district_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>period</span>\" => \"<span class=sf-dump-str title=\"5 characters\">month</span>\"
    \"<span class=sf-dump-key>rental_period</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>required_documents</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>suitable_for</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">renting</span>\"
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-num>114</span>
    \"<span class=sf-dump-key>author_type</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Xmetr\\RealEstate\\Models\\Account</span>\"
    \"<span class=sf-dump-key>moderation_status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">approved</span>\"
    \"<span class=sf-dump-key>reject_reason</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>expire_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-05-21</span>\"
    \"<span class=sf-dump-key>auto_renew</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>bills_included</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>utilities</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>furnished</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>pets_allowed</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>smoking_allowed</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>online_view_tour</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>never_expired</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-21 20:57:22</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-21 20:59:45</span>\"
    \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-34.5800974</span>\"
    \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-58.4417991</span>\"
    \"<span class=sf-dump-key>views</span>\" => <span class=sf-dump-num>153</span>
    \"<span class=sf-dump-key>whatsapp_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>telegram_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>phone_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>22</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>unique_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>private_notes</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:31</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Xmetr\\RealEstate\\Enums\\PropertyStatusEnum</span>\"
    \"<span class=sf-dump-key>moderation_status</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Xmetr\\RealEstate\\Enums\\ModerationStatusEnum</span>\"
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Xmetr\\RealEstate\\Enums\\PropertyTypeEnum</span>\"
    \"<span class=sf-dump-key>period</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Xmetr\\RealEstate\\Enums\\PropertyPeriodEnum</span>\"
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>private_notes</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>expire_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"
    \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>commission</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>deposit</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>square</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>number_bedroom</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>number_bathroom</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>number_floor</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>bills_included</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>utilities</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>furnished</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>pets_allowed</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>smoking_allowed</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>online_view_tour</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>floor_plans</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"
    \"<span class=sf-dump-key>whatsapp_clicks</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>telegram_clicks</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>phone_clicks</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>rental_period</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Xmetr\\RealEstate\\Enums\\RentalPeriodEnum</span>\"
    \"<span class=sf-dump-key>required_documents</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"
    \"<span class=sf-dump-key>suitable_for</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>status</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Enums\\PropertyStatusEnum
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">PropertyStatusEnum</span></span> {<a class=sf-dump-ref>#4575</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"7 characters\">renting</span>\"
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>slugable</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Slug\\Models\\Slug
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Slug\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Slug</span></span> {<a class=sf-dump-ref>#4633</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">slugs</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>state</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Location\\Models\\State
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Location\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">State</span></span> {<a class=sf-dump-ref>#4576</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">states</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>city</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Location\\Models\\City
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Location\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">City</span></span> {<a class=sf-dump-ref>#4642</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">cities</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>currency</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Currency
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Currency</span></span> {<a class=sf-dump-ref>#4634</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">re_currencies</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>categories</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4640</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:44</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"20 characters\">original_description</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"7 characters\">content</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">location</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"5 characters\">video</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"15 characters\">video_thumbnail</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"10 characters\">project_id</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">number_bedroom</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"15 characters\">number_bathroom</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"12 characters\">number_floor</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"6 characters\">square</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"10 characters\">commission</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"7 characters\">deposit</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"
    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"11 characters\">currency_id</span>\"
    <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"7 characters\">city_id</span>\"
    <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"11 characters\">district_id</span>\"
    <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"8 characters\">state_id</span>\"
    <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"10 characters\">country_id</span>\"
    <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"6 characters\">period</span>\"
    <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"9 characters\">author_id</span>\"
    <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"11 characters\">author_type</span>\"
    <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">expire_date</span>\"
    <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"10 characters\">auto_renew</span>\"
    <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"14 characters\">bills_included</span>\"
    <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"9 characters\">utilities</span>\"
    <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"9 characters\">furnished</span>\"
    <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"12 characters\">pets_allowed</span>\"
    <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"15 characters\">smoking_allowed</span>\"
    <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"16 characters\">online_view_tour</span>\"
    <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">latitude</span>\"
    <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"9 characters\">longitude</span>\"
    <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">unique_id</span>\"
    <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"13 characters\">private_notes</span>\"
    <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"11 characters\">floor_plans</span>\"
    <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"13 characters\">reject_reason</span>\"
    <span class=sf-dump-index>41</span> => \"<span class=sf-dump-str title=\"13 characters\">rental_period</span>\"
    <span class=sf-dump-index>42</span> => \"<span class=sf-dump-str title=\"18 characters\">required_documents</span>\"
    <span class=sf-dump-index>43</span> => \"<span class=sf-dump-str title=\"12 characters\">suitable_for</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","images":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">https://media.xmetr.com/accounts/m-105/1.webp</span>\"
  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"46 characters\">https://media.xmetr.com/accounts/m-106/11.webp</span>\"
  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"47 characters\">https://media.xmetr.com/accounts/m-107/111.webp</span>\"
  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"48 characters\">https://media.xmetr.com/accounts/m-108/1111.webp</span>\"
  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"49 characters\">https://media.xmetr.com/accounts/m-109/11111.webp</span>\"
  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"50 characters\">https://media.xmetr.com/accounts/m-110/111111.webp</span>\"
  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"51 characters\">https://media.xmetr.com/accounts/m-111/1111111.webp</span>\"
  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"52 characters\">https://media.xmetr.com/accounts/m-112/********.webp</span>\"
  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"53 characters\">https://media.xmetr.com/accounts/m-113/*********.webp</span>\"
  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/accounts/m-114/*********1.webp</span>\"
  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"55 characters\">https://media.xmetr.com/accounts/m-115/*********11.webp</span>\"
  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"57 characters\">https://media.xmetr.com/accounts/m-116/*********1111.webp</span>\"
  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"58 characters\">https://media.xmetr.com/accounts/m-117/*********11111.webp</span>\"
  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"61 characters\">https://media.xmetr.com/accounts/m-118/*****************.webp</span>\"
  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"62 characters\">https://media.xmetr.com/accounts/m-119/******************.webp</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","style":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str>5</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","data":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Country house with golf course views in La Reserva Cardales</span>\"
  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">$ 900 </span>\"
  \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Buenos Aires</span>\"
  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Argentina</span>\"
  \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"5 characters\">House</span>\"
  \"<span class=sf-dump-key>number_bedroom</span>\" => <span class=sf-dump-num>3</span>
  \"<span class=sf-dump-key>number_bathroom</span>\" => <span class=sf-dump-num>2</span>
  \"<span class=sf-dump-key>number_floor</span>\" => <span class=sf-dump-const>null</span>
  \"<span class=sf-dump-key>square</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0 &#1084;&#178;</span>\"
  \"<span class=sf-dump-key>deposit</span>\" => \"\"
  \"<span class=sf-dump-key>commission</span>\" => \"\"
  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"95 characters\">House with Golf View. 3 Bedrooms, 2 Bathrooms. With Grill in Front of Kitchen. Garden and Pool.</span>\"
  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"45 characters\">https://media.xmetr.com/accounts/m-105/1.webp</span>\"
  \"<span class=sf-dump-key>property_url</span>\" => \"<span class=sf-dump-str title=\"88 characters\">https://xmetr.gc/en/properties/zagorodnyi-dom-s-vidom-na-golf-pole-v-la-reserva-cardales</span>\"
  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">https://media.xmetr.com/accounts/m-105/1.webp</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"46 characters\">https://media.xmetr.com/accounts/m-106/11.webp</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"47 characters\">https://media.xmetr.com/accounts/m-107/111.webp</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"48 characters\">https://media.xmetr.com/accounts/m-108/1111.webp</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"49 characters\">https://media.xmetr.com/accounts/m-109/11111.webp</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"50 characters\">https://media.xmetr.com/accounts/m-110/111111.webp</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"51 characters\">https://media.xmetr.com/accounts/m-111/1111111.webp</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"52 characters\">https://media.xmetr.com/accounts/m-112/********.webp</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"53 characters\">https://media.xmetr.com/accounts/m-113/*********.webp</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/accounts/m-114/*********1.webp</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"55 characters\">https://media.xmetr.com/accounts/m-115/*********11.webp</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"57 characters\">https://media.xmetr.com/accounts/m-116/*********1111.webp</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"58 characters\">https://media.xmetr.com/accounts/m-117/*********11111.webp</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"61 characters\">https://media.xmetr.com/accounts/m-118/*****************.webp</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"62 characters\">https://media.xmetr.com/accounts/m-119/******************.webp</span>\"
  </samp>]
  \"<span class=sf-dump-key>agent</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Rina F</span>\"
  \"<span class=sf-dump-key>agent_phone</span>\" => \"<span class=sf-dump-str title=\"11 characters\">***********</span>\"
  \"<span class=sf-dump-key>agent_email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"
  \"<span class=sf-dump-key>agent_whatsapp</span>\" => \"<span class=sf-dump-str title=\"11 characters\">***********</span>\"
  \"<span class=sf-dump-key>agent_telegram</span>\" => \"<span class=sf-dump-str title=\"5 characters\">@rina</span>\"
  \"<span class=sf-dump-key>agent_url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">https://xmetr.gc/en/agents/rina</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","deposit":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","image":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"38 characters\">accounts/m-119/******************.webp</span>\"
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","account":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Xmetr\\RealEstate\\Models\\Account</span> {<a class=sf-dump-ref>#4258</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_accounts</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:27</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>15</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Rina</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str>F</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"\"
    \"<span class=sf-dump-key>gender</span>\" => \"<span class=sf-dump-str title=\"4 characters\">male</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"2 characters\">&#1052;.</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$mPh0Jh3iv75UdQt8DSfIc.Q483pgIY4lByIfF1zG8NgYOriPrgZaO</span>\"
    \"<span class=sf-dump-key>avatar_id</span>\" => <span class=sf-dump-num>178</span>
    \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2024-12-07</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"11 characters\">***********</span>\"
    \"<span class=sf-dump-key>credits</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>confirmed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-11 20:01:55</span>\"
    \"<span class=sf-dump-key>email_verify_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"60 characters\">7bukzX3rvfEeiLWzCaNMn4h7uKhK0XgrLWekdOAYFauTTNxz1JB3ojsVo0AC</span>\"
    \"<span class=sf-dump-key>last_login</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-14 17:04:49</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-11 22:01:55</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-14 17:04:49</span>\"
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_verified</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>is_public_profile</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>account_type</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Enums\\AccountTypeEnum
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AccountTypeEnum</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24537 title=\"2 occurrences\">#4537</a><samp data-depth=3 id=sf-dump-*********-ref24537 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"5 characters\">agent</span>\"
    </samp>}
    \"<span class=sf-dump-key>company</span>\" => \"\"
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>22</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>approved_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:27</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>15</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Rina</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str>F</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"\"
    \"<span class=sf-dump-key>gender</span>\" => \"<span class=sf-dump-str title=\"4 characters\">male</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"2 characters\">&#1052;.</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$mPh0Jh3iv75UdQt8DSfIc.Q483pgIY4lByIfF1zG8NgYOriPrgZaO</span>\"
    \"<span class=sf-dump-key>avatar_id</span>\" => <span class=sf-dump-num>178</span>
    \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2024-12-07</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"11 characters\">***********</span>\"
    \"<span class=sf-dump-key>credits</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>confirmed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-11 20:01:55</span>\"
    \"<span class=sf-dump-key>email_verify_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"60 characters\">7bukzX3rvfEeiLWzCaNMn4h7uKhK0XgrLWekdOAYFauTTNxz1JB3ojsVo0AC</span>\"
    \"<span class=sf-dump-key>last_login</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-14 17:04:49</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-11 22:01:55</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-14 17:04:49</span>\"
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_verified</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>is_public_profile</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>account_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">agent</span>\"
    \"<span class=sf-dump-key>company</span>\" => \"\"
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>22</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>approved_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:16</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>package_start_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>package_end_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>is_public_profile</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>is_verified</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>company</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">hashed</span>\"
    \"<span class=sf-dump-key>approved_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>last_login</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>account_type</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Xmetr\\RealEstate\\Enums\\AccountTypeEnum</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>account_type</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Enums\\AccountTypeEnum
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AccountTypeEnum</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24537 title=\"2 occurrences\">#4537</a>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>metadata</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4372</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>slugable</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Slug\\Models\\Slug
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Slug\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Slug</span></span> {<a class=sf-dump-ref>#4462</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">slugs</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>avatar</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Media\\Models\\MediaFile
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Media\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MediaFile</span></span> {<a class=sf-dump-ref>#4560</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">media_files</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:13</span> [ &#8230;13]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:13</span> [ &#8230;13]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>spokenLanguages</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4528</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">first_name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">last_name</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">username</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"9 characters\">avatar_id</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"3 characters\">dob</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">gender</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">company</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"10 characters\">country_id</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"8 characters\">state_id</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"7 characters\">city_id</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"17 characters\">is_public_profile</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"12 characters\">account_type</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">is_verified</span>\"
    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"10 characters\">last_login</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","whatsapp":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"11 characters\">***********</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","telegram":"<pre class=sf-dump id=sf-dump-132152332 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"5 characters\">@rina</span>\"
</pre><script>Sfdump(\"sf-dump-132152332\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","commission":"<pre class=sf-dump id=sf-dump-265049926 data-indent-pad=\"  \">\"\"
</pre><script>Sfdump(\"sf-dump-265049926\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","relatedProperties":"<pre class=sf-dump id=sf-dump-1349171218 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#4578</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Property
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Property</span></span> {<a class=sf-dump-ref>#4577</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">re_properties</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:53</span> [ &#8230;53]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:53</span> [ &#8230;53]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:31</span> [ &#8230;31]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:44</span> [ &#8230;44]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1349171218\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","relatedPropertiesCount":"<pre class=sf-dump id=sf-dump-1571055855 data-indent-pad=\"  \"><span class=sf-dump-num>1</span>
</pre><script>Sfdump(\"sf-dump-1571055855\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-2046313615 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#4578</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Property
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Property</span></span> {<a class=sf-dump-ref>#4577</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">re_properties</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:53</span> [ &#8230;53]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:53</span> [ &#8230;53]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:31</span> [ &#8230;31]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:44</span> [ &#8230;44]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-2046313615\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-294513284 data-indent-pad=\"  \">{<a class=sf-dump-ref>#4590</a><samp data-depth=1 class=sf-dump-expanded>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">iteration</span>\": <span class=sf-dump-num>1</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">index</span>\": <span class=sf-dump-num>0</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">remaining</span>\": <span class=sf-dump-num>0</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">count</span>\": <span class=sf-dump-num>1</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">first</span>\": <span class=sf-dump-const>true</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">last</span>\": <span class=sf-dump-const>true</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">odd</span>\": <span class=sf-dump-const>true</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">even</span>\": <span class=sf-dump-const>false</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">depth</span>\": <span class=sf-dump-num>1</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">parent</span>\": <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-294513284\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $rented_overlay at D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\properties\\item-grid.blade.php:79)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#7 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\single-layouts\\partials\\related-properties.blade.php(16): Illuminate\\View\\View->render()
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#15 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\single-layouts\\style-5.blade.php(148): Illuminate\\View\\View->render()
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#20 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#21 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#22 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#23 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\property.blade.php(52): Illuminate\\View\\View->render()
#24 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#25 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#27 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#28 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#29 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#30 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#31 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(682): Illuminate\\View\\View->render()
#32 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(661): Xmetr\\Theme\\Theme->setUpContent('theme.xmetr::vi...', Array)
#33 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Xmetr\\Theme\\Theme->scope('real-estate.pro...', Array, 'plugins/real-es...')
#34 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(86): Illuminate\\Support\\Facades\\Facade::__callStatic('scope', Array)
#35 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(116): Xmetr\\Theme\\Http\\Controllers\\PublicController->getView('zagorodnyi-dom-...', 'properties')
#36 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Xmetr\\Theme\\Http\\Controllers\\PublicController->getViewWithPrefix('properties', 'zagorodnyi-dom-...')
#37 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getViewWithPref...', Array)
#38 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Xmetr\\Theme\\Http\\Controllers\\PublicController), 'getViewWithPref...')
#39 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#40 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#41 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->Xmetr\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\xmetr\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIncorrectLanguagePrefix->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#71 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#78 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#80 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#101 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#102 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#103 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#104 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#105 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $rented_overlay at D:\\laragon\\www\\xmetr\\storage\\framework\\views\\dc0f09b9fd3117a5fd1f437616c97cae.php:80)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'D:\\\\laragon\\\\www\\\\...', 80)
#1 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\dc0f09b9fd3117a5fd1f437616c97cae.php(80): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'D:\\\\laragon\\\\www\\\\...', 80)
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#9 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\ccef28b1743cfd73b42ae2fd0d3a6dd4.php(16): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#17 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\ad88605931b659db0874ab390e4ac9a6.php(137): Illuminate\\View\\View->render()
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#20 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#21 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#23 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#24 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#25 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\03b54f071e37eb41b5e8510b59b8e693.php(53): Illuminate\\View\\View->render()
#26 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#27 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#28 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#29 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#30 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#31 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#32 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#33 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(682): Illuminate\\View\\View->render()
#34 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(661): Xmetr\\Theme\\Theme->setUpContent('theme.xmetr::vi...', Array)
#35 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Xmetr\\Theme\\Theme->scope('real-estate.pro...', Array, 'plugins/real-es...')
#36 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(86): Illuminate\\Support\\Facades\\Facade::__callStatic('scope', Array)
#37 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(116): Xmetr\\Theme\\Http\\Controllers\\PublicController->getView('zagorodnyi-dom-...', 'properties')
#38 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Xmetr\\Theme\\Http\\Controllers\\PublicController->getViewWithPrefix('properties', 'zagorodnyi-dom-...')
#39 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getViewWithPref...', Array)
#40 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Xmetr\\Theme\\Http\\Controllers\\PublicController), 'getViewWithPref...')
#41 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#42 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#43 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->Xmetr\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\xmetr\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIncorrectLanguagePrefix->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#73 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#80 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#82 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#93 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#94 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#101 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#102 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#103 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#104 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#105 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#106 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#107 {main}
"} 
[2025-08-17 18:50:31] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 18:51:00] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 18:52:00] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 18:52:26] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 18:52:27] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 19:37:19] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 19:37:53] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 19:39:40] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 19:39:41] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 19:40:14] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 19:40:45] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 19:41:06] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 19:48:30] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 19:48:53] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 21:18:28] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 22:22:02] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 22:22:04] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 22:22:51] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 22:25:03] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 22:25:51] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 22:26:50] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 22:53:50] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 22:55:14] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 22:55:28] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:04:52] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:09:44] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:10:18] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:10:36] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:10:39] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:11:54] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:12:06] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:15:57] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:16:13] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:16:17] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:19:51] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:20:04] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:20:24] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:20:43] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:21:00] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:25:45] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:26:08] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-08-17 23:26:25] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
